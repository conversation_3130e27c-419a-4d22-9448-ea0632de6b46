// Dart imports:
import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:ui' as ui;

// Flutter imports:
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// Package imports:
import 'package:auto_route/auto_route.dart';
import 'package:ez_intl/ez_intl.dart';
import 'package:pro_image_editor/designs/frosted_glass/frosted_glass.dart';
import 'package:pro_image_editor/pro_image_editor.dart';
import 'package:pro_image_editor/shared/widgets/layer/interaction_helper/layer_interaction_border_painter.dart';

// Project imports:
import '../../../core/nd_constants/strings.dart';
import '../../../core/params/sticker_set_request_params.dart';
import '../../../core/utils/nd_utils.dart';
import '../../../domain/entities/sticker_set.dart';
import '../../../domain/entities/sticker_social.dart';
import '../../widgets/value_notifier_list.dart';
import '../widgets/story_edit_image_sticker.dart';
import 'story_preview_image_write_page.dart';


@RoutePage()
class StoryEditImagePage extends StatefulWidget {
  const StoryEditImagePage({
    super.key,
    required this.file,
    required this.i,
    this.isUpdate = false,
  });
  final bool isUpdate;
  final File file;
  final int i;

  @override
  State<StoryEditImagePage> createState() => _StoryEditImagePageState();
}

class _StoryEditImagePageState extends State<StoryEditImagePage> {
  final editorKey = GlobalKey<ProImageEditorState>();
  StickerSetRequestParams params = const StickerSetRequestParams(page: 1);
  Uint8List? editedBytes;
  double? _generationTime;
  DateTime? startEditingTime;
  Future<void> onImageEditingStarted() async {
    startEditingTime = DateTime.now();
  }

  Future<void> onImageEditingComplete(
    final Uint8List bytes, {
    final bool showThumbnail = false,
    final ui.Image? rawOriginalImage,
    required final int index,
  }) async {
    editedBytes = bytes;
    if (editedBytes != null) {
      await precacheImage(MemoryImage(editedBytes!), context);
      if (!mounted) {
        return;
      }
      editorKey.currentState?.isPopScopeDisabled = true;
      await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (final context) {
            return PreviewImgPage(
              imgBytes: editedBytes ?? Uint8List(0),
              generationTime: _generationTime,
              showThumbnail: showThumbnail,
              rawOriginalImage: rawOriginalImage,
              index: widget.i,
              isUpdate: widget.isUpdate,
            );
          },
        ),
      ).whenComplete(() {
        editedBytes = null;
        _generationTime = null;
        startEditingTime = null;
      });
    }
    setGenerationTime();
  }

  Future<void> onCloseEditor(final EditorMode mode) async {
    if (mounted) {
      Navigator.pop(context);
    }
  }

  void setGenerationTime() {
    if (startEditingTime != null) {
      _generationTime = DateTime.now()
          .difference(startEditingTime!)
          .inMilliseconds
          .toDouble();
    }
  }

  dynamic mapEmoji = {};
  List<String> data = [];
  final ValueNotifierList<StickerSocialItems> sticker = ValueNotifierList([]);
  final ValueNotifierList<StickerSetSocialItems> sets = ValueNotifierList([]);
  Future<void> _fetchEmoji() async {
    final String repo = await rootBundle.loadString('assets/emoji/emoji.json');
    mapEmoji = await jsonDecode(repo);
    // ignore: avoid_dynamic_calls
    data = [...(mapEmoji['emoji'] as List).map((final e) => e)];
    setState(() {});
  }
  
  
  ValueNotifierList<String> stickersRecent = ValueNotifierList([]);
  @override
  void initState() {
    super.initState();
    unawaited(_fetchEmoji());
  }

  @override
  Widget build(final BuildContext context) {
    return ProImageEditor.file(
      widget.file,
      callbacks: ProImageEditorCallbacks(
        onImageEditingStarted: onImageEditingStarted,
        onImageEditingComplete: (final unit8List) async {
          return onImageEditingComplete(unit8List, index: widget.i);
        },
        onCloseEditor: onCloseEditor,
      ),
      configs: ProImageEditorConfigs(
        i18n: const I18n(
          paintEditor: I18nPaintEditor(bottomNavigationBarText: Strings.empty),
          textEditor: I18nTextEditor(bottomNavigationBarText: Strings.empty),
          cropRotateEditor: I18nCropRotateEditor(
            bottomNavigationBarText: Strings.empty,
          ),
          stickerEditor: I18nStickerEditor(
            bottomNavigationBarText: Strings.empty,
          ),
          filterEditor: I18nFilterEditor(
            bottomNavigationBarText: Strings.empty,
          ),
          blurEditor: I18nBlurEditor(bottomNavigationBarText: Strings.empty),
        ),
        designMode: ImageEditorDesignMode.cupertino,
        layerInteraction: LayerInteractionConfigs(
          widgets: LayerInteractionWidgets(
            border: (final layerWidget, final layerData) => Container(
              margin: const EdgeInsets.all(10),
              child: CustomPaint(
                foregroundPainter: LayerInteractionBorderPainter(
                  style: const LayerInteractionStyle(),
                ),
                child: layerWidget,
              ),
            ),
          ),
        ),
        paintEditor: PaintEditorConfigs(
          widgets: PaintEditorWidgets(
            appBar: (final paintEditor, final rebuildStream) => ReactiveAppbar(
              builder: (final _) => _appBarPaintingEditor(paintEditor),
              stream: rebuildStream,
            ),
            colorPicker:
                (
                  final paintEditor,
                  final rebuildStream,
                  final currentColor,
                  final setColor,
                ) => ReactiveWidget(
                  stream: rebuildStream,
                  builder: (final cxt) =>
                      FrostedGlassPaintBottomBar(paintEditor: paintEditor),
                ),
          ),
        ),
        stickerEditor: StickerEditorConfigs(
          enabled: true,
          builder: (final setLayer, final scrollController) {
            return StoryEditImageSticker(setLayer: setLayer);
          },
        ),
        textEditor: TextEditorConfigs(
          showSelectFontStyleBottomBar: true,
          customTextStyles: [
            GoogleFonts.roboto(),
            GoogleFonts.averiaLibre(),
            GoogleFonts.lato(),
            GoogleFonts.comicNeue(),
            GoogleFonts.actor(),
            GoogleFonts.odorMeanChey(),
            GoogleFonts.nabla(),
          ],
        ),
        emojiEditor: const EmojiEditorConfigs(enabled: false),
        tuneEditor: const TuneEditorConfigs(enabled: false),
        filterEditor: const FilterEditorConfigs(
          icons: FilterEditorIcons(bottomNavBar: Icons.wb_sunny),
        ),
        mainEditor: MainEditorConfigs(
          widgets: MainEditorWidgets(
            closeWarningDialog: (final state) async {
              return await showDialog<bool>(
                    context: context,
                    builder: (final BuildContext context) => AlertDialog(
                      content: Text(
                        context.l10n.imageEdit,
                        textAlign: TextAlign.center,
                      ),
                      actions: <Widget>[
                        DecoratedBox(
                          decoration: BoxDecoration(
                            color: Theme.of(context).scaffoldBackgroundColor,
                            borderRadius: const BorderRadius.only(
                              bottomLeft: Radius.circular(8),
                              bottomRight: Radius.circular(8),
                            ),
                          ),
                          child: Center(
                            child: Row(
                              children: [
                                Expanded(
                                  child: GestureDetector(
                                    behavior: HitTestBehavior.translucent,
                                    onTap: () => Navigator.pop(context, false),
                                    child: Center(
                                      child: Padding(
                                        padding: const EdgeInsets.symmetric(
                                          vertical: 10,
                                          horizontal: 24,
                                        ),
                                        child: Text(
                                          context.l10n.cancel,
                                          style: Theme.of(context)
                                              .textTheme
                                              .bodyMedium
                                              ?.copyWith(
                                                fontWeight: FontWeight.w500,
                                              ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                                Expanded(
                                  child: GestureDetector(
                                    behavior: HitTestBehavior.translucent,
                                    onTap: () => Navigator.pop(context, true),
                                    child: Center(
                                      child: Padding(
                                        padding: const EdgeInsets.symmetric(
                                          vertical: 10,
                                          horizontal: 24,
                                        ),
                                        child: Text(
                                          context.l10n.confirm,
                                          style: Theme.of(context)
                                              .textTheme
                                              .bodyMedium
                                              ?.copyWith(
                                                fontWeight: FontWeight.w500,
                                                color: Theme.of(
                                                  context,
                                                ).primaryColor,
                                              ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ) ??
                  false;
            },
            removeLayerArea:
                (final key, final editor, final rebuildStream, final isRemove) {
                  return Positioned(
                    key: key,
                    bottom: 50,
                    left: MediaQuery.sizeOf(context).width / 2 - 30,
                    right: MediaQuery.sizeOf(context).width / 2 - 30,
                    child: SafeArea(
                      bottom: false,
                      child: AnimatedOpacity(
                        duration: const Duration(milliseconds: 300),
                        opacity: isRemove ? 1 : 0,
                        child: StreamBuilder(
                          stream: rebuildStream,
                          builder: (final context, final snapshot) {
                            if (editor.layerInteractionManager.hoverRemoveBtn) {
                              HapticFeedback.mediumImpact();
                            }
                            return AnimatedContainer(
                              duration: const Duration(milliseconds: 300),
                              curve: Curves.fastOutSlowIn,
                              height:
                                  editor.layerInteractionManager.hoverRemoveBtn
                                  ? 72
                                  : 56,
                              width:
                                  editor.layerInteractionManager.hoverRemoveBtn
                                  ? 72
                                  : 56,
                              decoration: BoxDecoration(
                                color:
                                    editor
                                        .layerInteractionManager
                                        .hoverRemoveBtn
                                    ? Colors.red
                                    : Colors.grey,
                                shape: BoxShape.circle,
                              ),
                              child: const Center(
                                child: Icon(Icons.delete_outline, size: 28),
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  );
                },
          ),
        ),
      ),
    );
  }

 

  AppBar _appBarPaintingEditor(final PaintEditorState paintEditor) {
    return AppBar(
      centerTitle: true,
      automaticallyImplyLeading: false,
      foregroundColor: Colors.white,
      backgroundColor: Colors.black,
      leading: IconButton(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        icon: const Icon(Icons.arrow_back),
        onPressed: paintEditor.close,
      ),
      title: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          IconButton(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            icon: const Icon(Icons.line_weight_rounded, color: Colors.white),
            onPressed: paintEditor.openLinWidthBottomSheet,
          ),
          IconButton(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            icon: Icon(
              paintEditor.fillBackground == true
                  ? Icons.format_color_reset
                  : Icons.format_color_fill,
              color: Colors.white,
            ),
            onPressed: paintEditor.toggleFill,
          ),
        ],
      ),
      actions: [
        IconButton(
          tooltip: 'Done',
          padding: const EdgeInsets.symmetric(horizontal: 8),
          icon: const Icon(Icons.done),
          iconSize: 28,
          onPressed: paintEditor.done,
        ),
      ],
    );
  }

}
