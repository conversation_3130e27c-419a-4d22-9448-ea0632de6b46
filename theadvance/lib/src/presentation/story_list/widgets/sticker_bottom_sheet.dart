// Dart imports:
// ignore_for_file: use_build_context_synchronously

// Dart imports:
import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'dart:ui';

// Flutter imports:
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

// Package imports:
import 'package:animations/animations.dart';
import 'package:auto_route/auto_route.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:collection/collection.dart';
import 'package:custom_image_crop/custom_image_crop.dart';
import 'package:extended_image/extended_image.dart';
import 'package:ez_core/ez_core.dart';
import 'package:ez_intl/ez_intl.dart';
import 'package:ez_resources/ez_resources.dart';
import 'package:glass/glass.dart';
import 'package:path_provider/path_provider.dart';
import 'package:visibility_detector/visibility_detector.dart';

// Project imports:
import '../../../core/nd_constants/strings.dart';
import '../../../core/nd_intl/nd_intl.dart';
import '../../../core/nd_progresshud/loading_widget.dart';
import '../../../core/params/alert_params.dart';
import '../../../core/params/sticker_request_params.dart';
import '../../../core/params/sticker_set_request_params.dart';
import '../../../core/params/sticker_set_update_request_params.dart';
import '../../../core/utils/nd_utils.dart';
import '../../../data/datasources/local/cache/hive/ez_cache.dart';
import '../../../domain/entities/entities.dart';
import '../../../domain/entities/social_upload_file.dart';
import '../../../domain/entities/sticker_set.dart';
import '../../../domain/entities/sticker_set_created_success.dart';
import '../../../domain/entities/sticker_social.dart';
import '../../../injector/injector.dart';
import '../../account/widgets/account_field.dart';
import '../../widgets/value_notifier_list.dart';
import '../bloc/sticker_bloc.dart';
import 'sticker_gif.dart';
import 'story_edit_image_sticker.dart';
import 'story_snapple.dart';
import 'visibily_video.dart';

class _OffsetLocal {
  _OffsetLocal(this.dyStart, this.dyEnd, this.position, this.idx);

  final double dyStart;
  final double dyEnd;
  final double position;
  final int idx;
}

class StickerBottomSheet extends StatefulWidget {
  const StickerBottomSheet({super.key, required this.onSelected});

  final Function(StickerSocialItems?) onSelected;
  @override
  State<StickerBottomSheet> createState() => _StickerBottomSheetState();
}

class _StickerBottomSheetState extends State<StickerBottomSheet>
    with TickerProviderStateMixin {
  final ValueNotifierList<StickerSocialItems> sticker = ValueNotifierList([]);
  final ValueNotifierList<StickerSetSocialItems> sets = ValueNotifierList([]);
  final GlobalKey<SnappableState> _snappableKey = GlobalKey<SnappableState>();
  StickerSetRequestParams params = const StickerSetRequestParams(page: 1);
  late DraggableScrollableController _controllerDraggable;
  ValueNotifierList<ChatGetUserStickerItemsStickers> stickersRecent =
      ValueNotifierList([]);
  @override
  void initState() {
    _controllerDraggable = DraggableScrollableController()
      ..addListener(() {
        if (_controllerDraggable.size <= 0.6000000000000006) {
          Navigator.of(context).pop();
        }
      });
    super.initState();
  }

  List<_OffsetLocal> mapOffset = [];
  List<_OffsetLocal> resultOffset = [];
  List<int> tabs = [];
  int initIndex = 0;

  @override
  Widget build(final BuildContext context) {
    return IconButton(
      onPressed: () {
        showModalBottomSheet(
          context: context,
          builder: (final builder) => Padding(
            padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom,
            ),
            child: DraggableScrollableSheet(
              initialChildSize: .6,
              controller: _controllerDraggable,
              minChildSize: .6,
              expand: false,
              snap: true,
              snapSizes: const [.6, 1],
              builder: (final context, final controller) {
                return ClipRRect(
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(20),
                  ),
                  child: SizedBox(
                    height: MediaQuery.sizeOf(context).height * .6,
                    child: BlocProvider(
                      create: (final context) =>
                          getIt<StickerBloc>()..add(StickerSetGet(params)),
                      //   ..add(StickerRecentGet()),
                      child: BlocConsumer<StickerBloc, StickerState>(
                        listener: (final context, final state) {
                          if (state.status == StickerStatus.updateSetSuccess) {
                            final StickerSetSocialItems data = Utils.getData(
                              state.data,
                            );
                            final idxSet = sets.value.indexWhere(
                              (final e) => e.id == data.id,
                            );
                            if (idxSet != -1) {
                              final getSet = sets.value[idxSet].copyWith(
                                name: data.name,
                                createdBy: data.createdBy,
                              );
                              sets.updateValuebyIndex(idxSet, getSet);
                            }
                          }
                          if (state.status == StickerStatus.removeSetSuccess) {
                            final setId = state.data.toString();
                            final indexW = sets.value.indexWhere(
                              (final e) => e.id == setId,
                            );
                            sets.removeIndex(indexW);
                          }
                          if (state.status == StickerStatus.setSuccess) {
                            final StickerSetSocial set = Utils.getData(
                              state.data,
                            );
                            final listSafe = set.items
                                ?.map((final e) => e)
                                .toList();
                            sets.setValue(listSafe);
                          }

                          if (state.status == StickerStatus.recentSuccess) {
                            final StickerSocial data = Utils.getData(
                              state.data,
                            );
                            final listSafe = data.item
                                ?.map((final e) => e)
                                .toList();
                            sticker.setValue(listSafe);
                          }
                          if (state.status == StickerStatus.updateSuccess) {
                            final StickerSocialItems sticker = Utils.getData(
                              state.data,
                            );
                            if (state.type == StikerTypePop.selected) {
                              widget.onSelected(sticker);
                            }
                          }
                          if (state.status == StickerStatus.createSuccess) {
                            final StickerSetCreatedSuccess data = Utils.getData(
                              state.data,
                            );

                            if (state.type == StikerTypePop.selected) {
                              // context.read<StickerBloc>().add(
                              //       StickerRecentUpdate(
                              //         StickerUpdateRequestParams(
                              //           setId: data.sticker?.setId,
                              //           id: data.sticker?.id,
                              //         ),
                              //         type: state.type,
                              //       ),
                              //     );
                              widget.onSelected(data.sticker);
                            } else {
                              final iSet = sets.value.indexWhere(
                                (final e) => e.id == data.set?.id,
                              );
                              if (iSet != -1) {
                                final item = sets.value[iSet];
                                item.stickers?.add(
                                  data.sticker ?? StickerSocialItems(),
                                );
                                sets.updateValuebyIndex(iSet, item);
                              } else {
                                final newSet = StickerSetSocialItems(
                                  id: data.set?.id,
                                  name: data.set?.name,
                                  createdBy: data.set?.createdBy,
                                  stickers: [
                                    data.sticker ?? StickerSocialItems(),
                                  ],
                                );
                                sets.add(newSet);
                              }
                            }
                          }
                          if (state.status == StickerStatus.uploadSuccess) {
                            final SocialUploadFile att = Utils.getData(
                              state.data,
                            );
                            context.read<StickerBloc>().add(
                              StickerCreate(
                                params:
                                    state.params?.copyWith(url: att.link) ??
                                    const StickerRequestParams(),
                                type: state.type,
                              ),
                            );
                          }
                        },
                        builder: (final context, final state) {
                          final ValueNotifier<TabController> tabController =
                              ValueNotifier(
                                TabController(
                                  initialIndex: initIndex,
                                  length: 0,
                                  vsync: this,
                                ),
                              );
                          return _buildStickerRecently(
                            tabController,
                            controller,
                          );
                        },
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        );
      },
      icon: const Icon(Icons.abc),
    );
  }

  ValueListenableBuilder<List<ChatGetUserStickerItemsStickers>>
  _buildStickerRecently(
    final ValueNotifier<TabController> tabController,
    final ScrollController controller,
  ) {
    return ValueListenableBuilder(
      valueListenable: stickersRecent,
      builder: (final context, final vStickersRecent, final child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ValueListenableBuilder(
              valueListenable: sets,
              builder: (final context, final vSets, final child) {
                final setsFilter = vSets
                    .where((final e) => (e.stickers?.length ?? 0) > 0)
                    .toList();
                tabController.value = TabController(
                  initialIndex: initIndex,
                  length:
                      setsFilter.length + (vStickersRecent.isNotEmpty ? 1 : 0),
                  vsync: this,
                );
                return Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: SizedBox(
                    width: double.infinity,
                    child: ValueListenableBuilder(
                      valueListenable: tabController,
                      builder: (final context, final vTab, final child) {
                        return TabBar(
                          labelPadding: const EdgeInsets.symmetric(
                            horizontal: 4,
                          ),
                          isScrollable: true,
                          tabAlignment: TabAlignment.start,
                          padding: EdgeInsets.zero,
                          indicatorSize: TabBarIndicatorSize.label,
                          dividerColor: Colors.transparent,
                          indicator: BoxDecoration(
                            color: Colors.blueGrey,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          controller: vTab,
                          tabs: [
                            if (vStickersRecent.isNotEmpty)
                              const Icon(Icons.abc),
                            ...List.generate(setsFilter.length, (final i) {
                              final item = setsFilter[i];
                              return _buildHeaderSetItem(item, context);
                            }),
                          ],
                        );
                      },
                    ),
                  ),
                );
              },
            ),
            _buildStickerBody(controller, tabController, vStickersRecent),
          ],
        );
      },
    );
  }

  Expanded _buildStickerBody(
    final ScrollController controller,
    final ValueNotifier<TabController> tabController,
    final List<ChatGetUserStickerItemsStickers> vStickersRecent,
  ) {
    controller.addListener(() {
      tabController.value.animateTo(tabs.min);
    });

    return Expanded(
      child: ValueListenableBuilder(
        valueListenable: sets,
        builder: (final context, final vSets, final child) {
          final setsFilter = vSets
              .where((final e) => (e.stickers?.length ?? 0) > 0)
              .toList();
          return ListView.builder(
            itemCount: (vStickersRecent.isNotEmpty ? 1 : 0) + setsFilter.length,
            controller: controller,
            itemBuilder: (final context, final iSet) {
              final setUser =
                  setsFilter[iSet + (vStickersRecent.isNotEmpty ? 1 : 0)];
              return iSet == 0 && vStickersRecent.isNotEmpty
                  ? VisibilityDetector(
                      key: const Key('RECENTLY'),
                      onVisibilityChanged: (final info) {
                        final visiblePercentage = info.visibleFraction * 100;
                        if (visiblePercentage <= 0.5) {
                          tabs.removeWhere((final e) => e == 0);
                        } else {
                          if (!tabs.contains(0)) {
                            tabs.add(0);
                          }
                        }
                      },
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Wrap(
                            spacing: 16,
                            runSpacing: 16,
                            children: [
                              ...List.generate(
                                vStickersRecent.reversed
                                    .toSet()
                                    .toList()
                                    .length,
                                (final i) {
                                  final item = ClipRRect(
                                    borderRadius: BorderRadius.circular(12),
                                    child: SizedBox(
                                      width: 60,
                                      height: 60,
                                      child:
                                          (vStickersRecent[i].mimetype
                                                  ?.contains('video') ??
                                              false)
                                          ? StickerGif(
                                              link: vStickersRecent[i].link,
                                            )
                                          : ExtendedImage.network(
                                              vStickersRecent[i].link ?? '',
                                            ),
                                    ),
                                  );
                                  return GestureDetector(
                                    onTap: () {
                                      final sticker =
                                          ChatGetUserStickerItemsStickers(
                                            id: vStickersRecent[i].id,
                                            stickerSetId:
                                                vStickersRecent[i].stickerSetId,
                                            link: vStickersRecent[i].link,
                                            size: vStickersRecent[i].size,
                                            mimetype:
                                                vStickersRecent[i].mimetype,
                                            width: vStickersRecent[i].width,
                                            height: vStickersRecent[i].height,
                                            icon: vStickersRecent[i].icon,
                                            filePath: '',
                                            tags: const [],
                                          );
                                      vStickersRecent.add(sticker);
                                      EZCache.shared.saveStickerRecent(
                                        vStickersRecent
                                            .map((final e) => e.toJson())
                                            .toList(),
                                      );
                                    },
                                    child: item,
                                  );
                                },
                              ),
                            ],
                          ),
                        ],
                      ),
                    )
                  : iSet == 0 && vStickersRecent.isEmpty
                  ? VisibilityDetector(
                      key: const Key('RECENTLY'),
                      onVisibilityChanged: (final info) {
                        final visiblePercentage = info.visibleFraction * 100;
                        if (visiblePercentage <= .5) {
                          tabs.removeWhere((final e) => e == 0);
                        } else {
                          if (!tabs.contains(0)) {
                            tabs.add(0);
                          }
                        }
                      },
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [_buildSetItem(setUser, context)],
                      ),
                    )
                  : VisibilityDetector(
                      key: Key(
                        setsFilter[iSet + (vStickersRecent.isNotEmpty ? 1 : 0)]
                                .id ??
                            '',
                      ),
                      onVisibilityChanged: (final info) {
                        final visiblePercentage = info.visibleFraction * 100;
                        if (visiblePercentage <= 1) {
                          tabs.removeWhere((final e) => e == 0);
                        } else {
                          if (!tabs.contains(
                            iSet + (vStickersRecent.isNotEmpty ? 1 : 0),
                          )) {
                            tabs.add(
                              iSet + (vStickersRecent.isNotEmpty ? 1 : 0),
                            );
                          }
                        }
                      },
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [_buildSetItem(setUser, context)],
                      ),
                    );
            },
          );
        },
      ),
    );
  }

  CustomCropShape currentShape = CustomCropShape.Square;
  CustomImageFit imageFit = CustomImageFit.fillCropSpace;

  // ignore: unused_element
  Future<dynamic> _showDropSticker(
    final BuildContext context,
    final ValueNotifier<File?> imageFile,
    final CustomImageCropController controller,
  ) {
    return showModal(
      context: context,
      builder: (final context) => AlertDialog(
        actionsAlignment: MainAxisAlignment.center,
        titlePadding: EdgeInsets.zero,
        insetPadding: EdgeInsets.zero,
        clipBehavior: Clip.antiAliasWithSaveLayer,
        title: _buildHeader(context, context.l10n.createStickers),
        content: SizedBox(
          width: MediaQuery.sizeOf(context).width,
          height: MediaQuery.sizeOf(context).height / 3,
          child: Stack(
            children: [
              ValueListenableBuilder(
                valueListenable: imageFile,
                builder: (final context, final vImageFile, final child) {
                  return vImageFile == null
                      ? const SizedBox()
                      : Stack(
                          children: [
                            Positioned.fill(
                              child: CustomImageCrop(
                                cropController: controller,
                                // forceInsideCropArea: true,
                                image: FileImage(vImageFile, scale: .9),
                                //// Any Imageprovider will work, try with a AssetImage or NetworkImage for example...
                                // image: const NetworkImage('https://upload.wikimedia.org/wikipedia/en/7/7d/Lenna_%28test_image%29.png'),
                                shape: currentShape,
                                borderRadius: 8,
                                customProgressIndicator:
                                    const CupertinoActivityIndicator(),
                                outlineColor: Colors.red,
                                imageFit: imageFit,
                                imageFilter: ImageFilter.blur(
                                  sigmaX: 10.0,
                                  sigmaY: 10.0,
                                ),
                              ),
                            ),
                            // Positioned.fill(
                            //   child: DottedBorder(
                            //     color: Theme.of(
                            //       context,
                            //     ).primaryColor,
                            //     dashPattern: const [
                            //       3,
                            //       9,
                            //     ],
                            //     strokeWidth: 4,
                            //     strokeCap: StrokeCap.round,
                            //     borderType: BorderType.RRect,
                            //     radius: const Radius.circular(
                            //       5,
                            //     ),
                            //     child: const SizedBox(),
                            //   ),
                            // ),
                            // Padding(
                            //   padding: const EdgeInsets.all(
                            //     2.0,
                            //   ),
                            //   child: Snappable(
                            //     key: _snappableKey,
                            //     child: CustomImageCrop(
                            //       borderRadius: 5,
                            //       cropPercentage: 1.24,
                            //       outlineColor:
                            //           Colors.white.withValues(alpha: 0),
                            //       shape: CustomCropShape.Square,
                            //       cropController: controller,
                            //       image: FileImage(
                            //         vImageFile,
                            //         scale: .9,
                            //       ),
                            //     ),
                            //   ),
                            // ),
                          ],
                        );
                },
              ),
            ],
          ),
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              final state = _snappableKey.currentState!;
              if (state.isGone) {
                state.reset();
              } else {
                state.snap();
              }
            },
            child: const Text('snap'),
          ),
          const SizedBox(height: 4),
          ElevatedButton(
            onPressed: () async {
              final memory = await controller.onCropImage();

              final ValueNotifier<bool> isOpen = ValueNotifier(false);
              if (context.mounted) {
                _openSticker(context, isOpen, memory?.bytes).then((
                  final val,
                ) async {
                  final isStickerType = val != null && val is StickerSave;
                  if (isStickerType) {
                    if (context.mounted) {
                      final tempDir = await getTemporaryDirectory();
                      final time = DateTime.now().formatDate3();
                      if (val.type == StikerTypePop.selected) {
                        File('${tempDir.path}/$time-image-temp.png')
                            .writeAsBytes(memory?.bytes ?? Uint8List(0))
                            .then((final file) {
                              if (context.mounted) {
                                context.router.popForced(
                                  StickerSave(
                                    val.params.copyWith(url: file.path),
                                    StikerTypePop.selected,
                                  ),
                                );
                              }
                            });
                      }
                      if (val.type == StikerTypePop.saveToLike) {
                        File('${tempDir.path}/$time-image-temp.png')
                            .writeAsBytes(memory?.bytes ?? Uint8List(0))
                            .then((final file) {
                              if (context.mounted) {
                                context.router.popForced(
                                  StickerSave(
                                    val.params.copyWith(url: file.path),
                                    StikerTypePop.saveToLike,
                                  ),
                                );
                              }
                            });
                      }
                      if (val.type == StikerTypePop.saveToSet) {
                        File('${tempDir.path}/$time-image-temp.png')
                            .writeAsBytes(memory?.bytes ?? Uint8List(0))
                            .then((final file) {
                              if (context.mounted) {
                                context.router.popForced(
                                  StickerSave(
                                    val.params.copyWith(url: file.path),
                                    StikerTypePop.saveToSet,
                                  ),
                                );
                              }
                            });
                      }
                    }
                  }
                });
              }
            },
            child: const Text('snap'),
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderSetItem(
    final StickerSetSocialItems item,
    final BuildContext context,
  ) {
    return Padding(
      padding: const EdgeInsets.all(4.0),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: ((item.isFavorite ?? false) && (item.category == 'default'))
            ? const Icon(Icons.star)
            : CachedNetworkImage(
                imageUrl: item.stickers?.firstOrNull?.link ?? '',
                width: 24,
                height: 24,
                placeholder: (final context, final url) =>
                    const LoadingWidget(),
                errorWidget: (final context, final url, final error) =>
                    const Icon(Icons.error),
              ),
      ),
    );
  }

  Padding _buildSetItem(
    final StickerSetSocialItems item,
    final BuildContext context,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(item.name ?? ''),
              GestureDetector(
                onTapDown: (final deital) {
                  final offset = deital.globalPosition;
                  showMenu(
                    context: context,
                    color: Colors.white,
                    surfaceTintColor: Colors.white,
                    position: RelativeRect.fromLTRB(
                      MediaQuery.sizeOf(context).width * .8,
                      offset.dy + 10,
                      30,
                      MediaQuery.sizeOf(context).height - offset.dy,
                    ),
                    items: [
                      PopupMenuItem<String>(
                        value: '',
                        child: const Text('sua ten'),
                        onTap: () {
                          final TextEditingController name =
                              TextEditingController(text: item.name);
                          showModal(
                            context: context,
                            builder: (final builder) {
                              return AlertDialog.adaptive(
                                title: const Text('Doi ten thu muc'),
                                content: AccountField(
                                  controller: name,
                                  isOnlyReady: false,
                                  label: '',
                                  hintText: 'nhap ten thu muc',
                                ),
                                actions: [
                                  TextButton(
                                    onPressed: () {
                                      Navigator.of(context).pop();
                                    },
                                    child: const Text('huy'),
                                  ),
                                  TextButton(
                                    onPressed: () {
                                      context.read<StickerBloc>().add(
                                        UpdateSet(
                                          StickerSetUpdateRequestParams(
                                            id: item.id,
                                            name: name.text.trim(),
                                          ),
                                        ),
                                      );
                                      Navigator.of(context).pop();
                                    },
                                    child: const Text('ok'),
                                  ),
                                ],
                              );
                            },
                          );
                        },
                      ),
                      PopupMenuItem<String>(
                        value: '',
                        child: const Text('Xoa'),
                        onTap: () {
                          Alert.showAlertConfirm(
                            AlertConfirmParams(
                              context,
                              message: context.l10n.doYouDeleteStory,
                              onPressed: () {
                                context.read<StickerBloc>().add(
                                  RemoveStickerSet(item.id ?? Strings.empty),
                                );
                                Navigator.of(context).pop();
                              },
                              confirmText: context.l10n.accept,
                              cancelButton: context.l10n.cancel,
                            ),
                          );
                        },
                      ),
                    ],
                  );
                },
                child: const Text('Chinh Sua'),
              ),
            ],
          ),
          if ((item.stickers ?? []).isNotEmpty)
            Wrap(
              spacing: 6,
              alignment: WrapAlignment.center,
              children: [
                ...List.generate(item.stickers?.length ?? 0, (final i2) {
                  final sticker = item.stickers?[i2];
                  final stickerW = ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: SizedBox(
                      width: 60,
                      height: 60,
                      child: (sticker?.mimetype?.contains('video') ?? false)
                          ? VisibilyVideo(link: sticker?.link ?? '')
                          : CachedNetworkImage(
                              imageUrl: sticker?.link ?? '',
                              placeholder: (final context, final url) =>
                                  const LoadingWidget(),
                              errorWidget:
                                  (final context, final url, final error) =>
                                      const Icon(Icons.error),
                            ),
                    ),
                  );
                  return Padding(
                    padding: const EdgeInsets.all(6.0),
                    child: GestureDetector(
                      onTap: () {
                        stickersRecent.add(
                          ChatGetUserStickerItemsStickers(
                            id: sticker?.id,
                            icon: sticker?.icon,
                            link: sticker?.link,
                            mimetype: sticker?.mimetype,
                            stickerSetId: sticker?.setId,
                            tags: const [],
                          ),
                        );

                        EZCache.shared.saveStickerRecent(
                          stickersRecent.value
                              .map((final e) => e.toJson())
                              .toList(),
                        );
                      },
                      child: stickerW,
                    ),
                  );
                }),
              ],
            )
          else
            const SizedBox(),
        ],
      ),
    );
  }

  Widget _buildHeader(final BuildContext context, final String title) {
    return ColoredBox(
      color: const Color(0xffF6F7FB),
      child: SizedBox(
        width: double.infinity,
        height: 48,
        child: Stack(
          children: [
            Align(
              alignment: Alignment.centerLeft,
              child: IconButton(
                onPressed: () async {
                  Navigator.of(context).pop();
                },
                icon: EZResources.image(ImageParams(name: AppIcons.icCancel)),
              ),
            ),
            Align(
              child: Text(title, style: Theme.of(context).textTheme.titleSmall),
            ),
          ],
        ),
      ),
    );
  }

  Future<dynamic> _openSticker(
    final BuildContext context,
    final ValueNotifier<bool> isOpen,
    final Uint8List? fileImage,
  ) {
    return showModal(
      context: context,
      builder: (final builder) {
        return GestureDetector(
          onTap: () {
            context.router.popForced();
          },
          child: AlertDialog.adaptive(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(0),
            ),
            backgroundColor: Colors.transparent,
            insetPadding: EdgeInsets.zero,
            contentPadding: EdgeInsets.zero,
            clipBehavior: Clip.antiAliasWithSaveLayer,
            content: SizedBox.expand(
              child: Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.amber,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      width: MediaQuery.sizeOf(context).width / 2,
                      height: MediaQuery.sizeOf(context).width / 2,
                      child: fileImage?.isNotEmpty ?? false
                          ? Image.memory(fileImage ?? Uint8List(0))
                          : Center(child: Text(context.l10n.pathNotFound)),
                    ),
                    const SizedBox(height: 12),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 12),
                      child: Stack(
                        children: [
                          SizedBox(
                            child: Column(
                              children: [
                                DecoratedBox(
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      _buildSelectedSticker(context),
                                      _buildSaveFavorite(context),
                                      ListTile(
                                        leading: const Icon(Icons.abc),
                                        title: const Text(
                                          'lưu vào mục cua ban',
                                        ),
                                        onTap: () {
                                          isOpen.value = true;
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 150),
                              ],
                            ),
                          ),
                          ValueListenableBuilder(
                            valueListenable: isOpen,
                            builder:
                                (final context, final vIsOpen, final child) {
                                  return AnimatedSize(
                                    duration: const Duration(milliseconds: 200),
                                    child: vIsOpen
                                        ? _buildOpenSet(isOpen, context)
                                        : const SizedBox(),
                                  );
                                },
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ).asGlass(),
        );
      },
    );
  }

  ListTile _buildSaveFavorite(final BuildContext context) {
    return ListTile(
      leading: const Icon(Icons.abc),
      title: const Text('lưu vào mục yêu thích'),
      onTap: () {
        context.router.popForced(
          StickerSave(
            const StickerRequestParams(
              setId: 'FAVORITE',
              label: 'tesst12',
              url: '',
            ),
            StikerTypePop.saveToLike,
          ),
        );
      },
    );
  }

  ListTile _buildSelectedSticker(final BuildContext context) {
    return ListTile(
      leading: const Icon(Icons.abc),
      title: const Text('chọn luôn'),
      onTap: () {
        context.router.popForced(
          StickerSave(
            const StickerRequestParams(
              setId: 'ANONYMOUS',
              label: 'tesst',
              url: '',
            ),
            StikerTypePop.selected,
          ),
        );
      },
    );
  }

  ColoredBox _buildOpenSet(
    final ValueNotifier<bool> isOpen,
    final BuildContext context,
  ) {
    return ColoredBox(
      color: Colors.white,
      child: SizedBox(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            IconButton(
              onPressed: () async {
                isOpen.value = false;
              },
              icon: EZResources.image(ImageParams(name: AppIcons.icBack)),
            ),
            ListTile(
              leading: const Icon(Icons.add),
              title: const Text('them muc moi'),
              onTap: () {
                final TextEditingController name = TextEditingController(
                  text: '',
                );
                showModal(
                  context: context,
                  builder: (final builder) {
                    return AlertDialog.adaptive(
                      title: const Text('tesst tao sticker'),
                      content: AccountField(
                        controller: name,
                        isOnlyReady: false,
                        label: '',
                        hintText: '',
                      ),
                      actions: [
                        TextButton(onPressed: () {}, child: const Text('huy')),
                        TextButton(
                          onPressed: () {
                            context.router.popForced();
                            context.router.popForced(
                              StickerSave(
                                StickerRequestParams(
                                  setName: name.text,
                                  label: 'tesstSet',
                                  url: '',
                                ),
                                StikerTypePop.saveToSet,
                              ),
                            );
                          },
                          child: const Text('ok'),
                        ),
                      ],
                    );
                  },
                );
              },
            ),
            SizedBox(
              width: MediaQuery.sizeOf(context).width / 2,
              height: 120,
              child: ListView.separated(
                itemBuilder: (final _, final i) {
                  final item = sets.value[i];
                  return ListTile(
                    onTap: () {
                      context.router.popForced(
                        StickerSave(
                          StickerRequestParams(
                            setId: item.id,
                            label: 'tesstSet',
                            url: '',
                          ),
                          StikerTypePop.saveToSet,
                        ),
                      );
                      // context.router.popForced(
                      //   _StickerSave(
                      //   StickerRequestParams(
                      //       label: 'tesstt123',
                      //       url: '',
                      //       setId: item.id,
                      //     ),
                      //     StikerTypePop.saveToSet,
                      // ),);
                    },
                    title: Text(item.name ?? ''),
                  );
                },
                separatorBuilder: (final _, final i) => const Divider(),
                itemCount: sets.value.length,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
