// Dart imports:
import 'dart:async';
import 'dart:io';

// Package imports:
import 'package:ez_core/ez_core.dart';
import 'package:injectable/injectable.dart';
import 'package:meta/meta.dart';

// Project imports:
import '../../../core/params/sticker_remove_request_params.dart';
import '../../../core/params/sticker_request_params.dart';
import '../../../core/params/sticker_set_request_params.dart';
import '../../../core/params/sticker_set_update_request_params.dart';
import '../../../core/params/sticker_update_request_params.dart';
import '../../../domain/usecases/sticker_social/create_sticker_usecase.dart';
import '../../../domain/usecases/sticker_social/get_sticker_only_set_usecase.dart';
import '../../../domain/usecases/sticker_social/get_sticker_recent_usecase.dart';
import '../../../domain/usecases/sticker_social/get_sticker_set_usecase.dart';
import '../../../domain/usecases/sticker_social/get_sticker_usecase.dart';
import '../../../domain/usecases/sticker_social/remove_sticker_set_usecase.dart';
import '../../../domain/usecases/sticker_social/remove_sticker_usecase.dart';
import '../../../domain/usecases/sticker_social/update_sticker_recent_usecase.dart';
import '../../../domain/usecases/sticker_social/update_sticker_set_usecase.dart';
import '../../../domain/usecases/sticker_social/upload_sticker_usecase.dart';
import '../widgets/story_edit_image_sticker.dart';

part 'sticker_event.dart';
part 'sticker_state.dart';

@injectable
class StickerBloc extends Bloc<StickerEvent, StickerState> {
  StickerBloc(
    this._createStickerListUseCase,
    this._getStickerListUseCase,
    this._getStickerSetUseCase,
    this._getStickerRecentUseCase,
    this._uploadFileUseCase,
    this._updateStickerRecentUseCase,
    this._getStickerOnlySetUseCase,
    this._removeStickerSetUseCase,
    this._removeStickerUseCase,
    this._updateStickerSetUseCase,
  ) : super(const StickerState(StickerStatus.init)) {
    on<StickerRecentGet>(_onStickerRecentGet);
    on<StickerGet>(_onStickerGet);
    on<StickerSetGet>(_onStickerSetGet);
    on<StickerCreate>(_onStickerCreate);
    on<StickerUploadFile>(_onStickerUpload);
    on<StickerRecentUpdate>(_onUpdateStickerRecent);
    on<StickerOnlySetGet>(_onStickerOnlySetGet);
    on<RemoveStickerSet>(_onRemoveStickerSet);
    on<RemoveSticker>(_onRemoveSticker);
    on<UpdateSet>(_onUpdateSet);
  }
  final CreateStickerListUseCase _createStickerListUseCase;
  final GetStickerListUseCase _getStickerListUseCase;
  final GetStickerSetUseCase _getStickerSetUseCase;
  final GetStickerRecentUseCase _getStickerRecentUseCase;
  final UploadStickerUseCase _uploadFileUseCase;
  final UpdateStickerRecentUseCase _updateStickerRecentUseCase;
  final GetStickerOnlySetUseCase _getStickerOnlySetUseCase;
  final RemoveStickerSetUseCase _removeStickerSetUseCase;
  final RemoveStickerUseCase _removeStickerUseCase;
  final UploadStickerSetUseCase _updateStickerSetUseCase;

  FutureOr<void> _onStickerGet(
    final StickerGet event,
    final Emitter<StickerState> emit,
  ) async {
    emit(state.copyWith(StickerStatus.loading));
    final dataState = await _getStickerListUseCase(params: ());
    if (dataState is DataSuccess) {
      emit(state.copyWith(StickerStatus.success, data: dataState.data));
    }
    if (dataState is DataFailure) {
      emit(state.copyWith(StickerStatus.failure, data: dataState.error));
    }
  }

  FutureOr<void> _onStickerSetGet(
    final StickerSetGet event,
    final Emitter<StickerState> emit,
  ) async {
    emit(state.copyWith(StickerStatus.loading));
    final dataState = await _getStickerSetUseCase(params: event.params);
    if (dataState is DataSuccess) {
      emit(state.copyWith(StickerStatus.setSuccess, data: dataState.data));
    }
    if (dataState is DataFailure) {
      emit(state.copyWith(StickerStatus.failure, data: dataState.error));
    }
  }

  FutureOr<void> _onStickerOnlySetGet(
    final StickerOnlySetGet event,
    final Emitter<StickerState> emit,
  ) async {
    emit(state.copyWith(StickerStatus.loading));
    final dataState = await _getStickerOnlySetUseCase(params: ());
    if (dataState is DataSuccess) {
      emit(state.copyWith(StickerStatus.onlySetSuccess, data: dataState.data));
    }
    if (dataState is DataFailure) {
      emit(state.copyWith(StickerStatus.failure, data: dataState.error));
    }
  }

  FutureOr<void> _onStickerRecentGet(
    final StickerRecentGet event,
    final Emitter<StickerState> emit,
  ) async {
    emit(state.copyWith(StickerStatus.loading));
    final dataState = await _getStickerRecentUseCase(params: ());
    if (dataState is DataSuccess) {
      emit(state.copyWith(StickerStatus.recentSuccess, data: dataState.data));
    }
    if (dataState is DataFailure) {
      emit(state.copyWith(StickerStatus.failure, data: dataState.error));
    }
  }

  FutureOr<void> _onStickerCreate(
    final StickerCreate event,
    final Emitter<StickerState> emit,
  ) async {
    emit(state.copyWith(StickerStatus.loading));
    final dataState = await _createStickerListUseCase(params: event.params);
    if (dataState is DataSuccess) {
      emit(
        state.copyWith(
          StickerStatus.createSuccess,
          data: dataState.data,
          type: event.type,
        ),
      );
    }
    if (dataState is DataFailure) {
      emit(state.copyWith(StickerStatus.failure, data: dataState.error));
    }
  }

  FutureOr<void> _onStickerUpload(
    final StickerUploadFile event,
    final Emitter<StickerState> emit,
  ) async {
    emit(state.copyWith(StickerStatus.loading));
    final dataState = await _uploadFileUseCase(params: event.files);
    if (dataState is DataSuccess) {
      emit(
        state.copyWith(
          StickerStatus.uploadSuccess,
          data: dataState.data,
          params: event.param,
          type: event.type,
        ),
      );
    }
    if (dataState is DataFailure) {
      emit(state.copyWith(StickerStatus.failure, data: dataState.error));
    }
  }

  FutureOr<void> _onUpdateStickerRecent(
    final StickerRecentUpdate event,
    final Emitter<StickerState> emit,
  ) async {
    emit(state.copyWith(StickerStatus.loading));
    final dataState = await _updateStickerRecentUseCase(params: event.params);
    if (dataState is DataSuccess) {
      emit(
        state.copyWith(
          StickerStatus.updateSuccess,
          data: dataState.data,
          type: event.type,
        ),
      );
    }
    if (dataState is DataFailure) {
      emit(state.copyWith(StickerStatus.failure, data: dataState.error));
    }
  }

  FutureOr<void> _onRemoveSticker(
    final RemoveSticker event,
    final Emitter<StickerState> emit,
  ) async {
    emit(state.copyWith(StickerStatus.loading));
    final dataState = await _removeStickerUseCase(params: event.params);
    if (dataState is DataSuccess) {
      emit(
        state.copyWith(StickerStatus.removeSetSuccess, data: dataState.data),
      );
    }
    if (dataState is DataFailure) {
      emit(state.copyWith(StickerStatus.failure, data: dataState.error));
    }
  }

  FutureOr<void> _onRemoveStickerSet(
    final RemoveStickerSet event,
    final Emitter<StickerState> emit,
  ) async {
    emit(state.copyWith(StickerStatus.loading));
    final dataState = await _removeStickerSetUseCase(params: event.params);
    if (dataState is DataSuccess) {
      emit(
        state.copyWith(StickerStatus.removeSetSuccess, data: dataState.data),
      );
    }
    if (dataState is DataFailure) {
      emit(state.copyWith(StickerStatus.failure, data: dataState.error));
    }
  }

  FutureOr<void> _onUpdateSet(
    final UpdateSet event,
    final Emitter<StickerState> emit,
  ) async {
    emit(state.copyWith(StickerStatus.loading));
    final dataState = await _updateStickerSetUseCase(params: event.params);
    if (dataState is DataSuccess) {
      emit(
        state.copyWith(StickerStatus.updateSetSuccess, data: dataState.data),
      );
    }
    if (dataState is DataFailure) {
      emit(state.copyWith(StickerStatus.failure, data: dataState.error));
    }
  }
}
